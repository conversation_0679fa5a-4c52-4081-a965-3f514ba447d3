# 数据源Bean冲突修复说明

## 问题描述

在启动Spring Boot应用时遇到以下错误：

```
The bean 'dataSource', defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class], could not be registered. A bean with that name has already been defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class] and overriding is disabled.
```

## 问题原因

项目同时使用了：
1. **Druid数据源自动配置** (`DruidDataSourceAutoConfigure`)
2. **Dynamic DataSource多数据源配置** (`DynamicDataSourceAutoConfiguration`)

两者都尝试创建名为`dataSource`的Bean，导致冲突。

## 解决方案

### 方案1：在启动类中排除Druid自动配置（已实施）

修改 `JeecgSystemApplication.java`：

```java
@SpringBootApplication(exclude = {com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.class})
```

### 方案2：在配置文件中允许Bean覆盖（已实施）

在 `application-dev.yml` 中添加：

```yaml
spring:
  main:
    allow-bean-definition-overriding: true
```

## 修改的文件

1. **jeecg-module-system/jeecg-system-start/src/main/java/org/jeecg/JeecgSystemApplication.java**
   - 在`@SpringBootApplication`注解中添加了`exclude`参数

2. **jeecg-module-system/jeecg-system-start/src/main/resources/application-dev.yml**
   - 添加了`spring.main.allow-bean-definition-overriding: true`配置

## 验证方法

创建了测试类 `DataSourceConfigTest.java` 来验证Bean冲突是否已解决：

```java
@SpringBootTest
@ActiveProfiles("dev")
public class DataSourceConfigTest {
    @Test
    public void contextLoads() {
        // 如果应用上下文能够成功加载，说明Bean冲突已解决
        System.out.println("Spring Boot应用上下文加载成功，Bean冲突已解决！");
    }
}
```

## 技术说明

- **Dynamic DataSource** 是项目的主要多数据源解决方案，应该保留
- **Druid自动配置** 与Dynamic DataSource冲突，需要排除
- 项目中的 `DruidConfig.java` 仍然可以正常工作，因为它是手动配置的Druid监控功能
- 配置文件中的Druid相关配置（如连接池参数）仍然有效，因为Dynamic DataSource内部使用Druid作为连接池

## 注意事项

1. 此修复不会影响现有的数据库连接功能
2. Druid监控页面仍然可以正常访问
3. 多数据源配置保持不变
4. 如果在其他环境（如prod、test）中也遇到同样问题，需要在对应的配置文件中也添加相同的配置

## 后续建议

1. 在所有环境的配置文件中都添加Bean覆盖配置，确保一致性
2. 定期检查依赖版本，避免类似的自动配置冲突
3. 考虑在CI/CD流程中添加启动测试，及早发现此类问题
