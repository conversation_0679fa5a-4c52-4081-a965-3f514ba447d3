package org.jeecg;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 数据源配置测试
 * 用于验证Bean冲突是否已解决
 */
@SpringBootTest
@ActiveProfiles("dev")
public class DataSourceConfigTest {

    @Test
    public void contextLoads() {
        // 如果应用上下文能够成功加载，说明Bean冲突已解决
        System.out.println("Spring Boot应用上下文加载成功，Bean冲突已解决！");
    }
}
