<!DOCTYPE html>
<html lang="zh_CN" id="htmlRoot">
<head>
  <meta charset="UTF-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta name="renderer" content="webkit"/>
  <meta
    name="viewport"
    content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=0"
  />
  <title><%= title %></title>
  <link rel="icon" href="/logo.png"/>
  <!--  <link href="/resource/activeReportJs/styles/ar-js-ui.css" rel="stylesheet"/>
   <link href="/resource/activeReportJs/styles/ar-js-viewer.css" rel="stylesheet"/>
  <link href="/resource/activeReportJs/styles/ar-js-designer.css" rel="stylesheet"/>-->

  <!-- 全局配置 -->
  <script>
    window._CONFIG = {
      // 项目部署基础路径
      domianURL: 'http://' + window.location.host,
      idSvrPort: '13131',
      idSvrLicense: 'ZA2qvYkz7kWy/RxL0eGpm07lwf895OlTmgOTktuGvOwKDWW7t78e8rJrxrCujNa9uKMz0GAtZmdoWLVA1xGqOh9G0O7/YZqj7IVpLePBH+ITa8iv8q+P87ao3hsq8cWCmU8ongbN8s2URo7wQUnnfEbu0vHqcbEb0SAjvFE6qT3OEH/jWLlbdrscDFdm/czgIieEpRtnkU8D4eUOkth59Dw7SqCRSyhA5nUx3kyBy0PqZpQz1ZRmBLJ5XKFeZ2aiq9iM+dfhkCNW3OmYwBjP5yLwKv2emHIOsVOtg3KqBLpERHCih8RekQ74po2kLK0QvR0Ru2/DzmPzAyhA211yQ5aM2Qw+Wm3MC0jFi+jkvAIrk2xqqO91CoS9Brj/M5ammTkRiGMnNWyhRvUgxXn/3tfrgGjYUMv28AygS86XXWvkuwU6h1cSjEeDS0B0TY9BvLHlfbi5afpdWGQtNA13MvXcULLcbxxbFUssQOF4DKLEZpQplm/Ro699vWyU7bnE2cV0Ys3RxyIuPrscdB9w83ljeuPfUdcza0W2IAmvYDd3+NjTzOSByYVCPdy8CYNYZAKXXEUga+qkSw1APYf3kTaVL+hIQeDWHHzV0MSsSYVCb24NyQWio+s1h/Bzp445UKEjvv7wYaGW6wawwQSsVEARnGLC/M+pFTAn6RzW1Gz2lh//kSwFyEwJhVXWWDO8hBqCj6/RRdNZy9NaE+wqmNWSSMF7uKIOMOyPI7MyA2Pv0/2f29rbum4o1GSzM1RhspO6nomoApE3a4jXU4m/UvuLDiicCy8uVMfhIVVSSZ/IHYS/0zPCq6gyfImjPXPT4+9eW2vamk1wm8zmCFv12gZDWOHijZNJ4e07mp2uLKPDab7lywYuNLzrwuoCO1WnT23bguXevm+jdt6jnXV2CPcfJGJTtvVouDlL/hcP2wTky8zNzad/OxJZYZz7++CZbIhUPUpKiS+hHMev4Bj8qjdS6S9iGpQ9AfXDKcoW1cLWAmCBdV2ALgwDxoWDEhdyPjlIf4n5JvmJBUvxlHKJT3EK1N+prToGS46elCn4kKhb1rjWqbvssReLt1Bzt1QMhNap4u6UvHrWwCzyVTgU+faxIQrB0XxWIbTO8rSTmW/yW8NXu5/9iOwyhh3nMh6wqAJAdu7nO9G1p70Cb1D6vfPM6qjmzEsqIazXQE8kmbel14ne4FNyggGbZrWwrnB0xYH/SDHTLBzaLvxhqxJlm0n0RQY5zLEbEGzrk2dg/x9Db9VJrbxzoUzz+U9AqwNYrlRsnKlLxB9IFtQIQ9izv4H11GhFuIwHNzDA2cR1+QqjRdWvNOkp0b9c17yBGbFL6WdAMt+7a2y0HLIZIun5Ra1V4ykIhu5hKmvfsX2cU='
    };
  </script>
</head>
<body>
<script>
/*  (() => {
    var htmlRoot = document.getElementById('htmlRoot');
    var theme = window.localStorage.getItem('__APP__DARK__MODE__');
    if (htmlRoot && theme) {
      htmlRoot.setAttribute('data-theme', theme);
      theme = htmlRoot = null;
    }
  })();*/
</script>

<div id="app">
  <style>
    html[data-theme='dark'] .app-loading {
      background-color: #2c344a;
    }

    html[data-theme='dark'] .app-loading .app-loading-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .app-loading {
      display: flex;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      background-color: #f4f7f9;
    }

    .app-loading .app-loading-wrap {
      position: absolute;
      top: 50%;
      left: 50%;
      display: flex;
      -webkit-transform: translate3d(-50%, -50%, 0);
      transform: translate3d(-50%, -50%, 0);
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .app-loading .dots {
      display: flex;
      padding: 98px;
      justify-content: center;
      align-items: center;
    }

    .app-loading .app-loading-title {
      display: flex;
      margin-top: 30px;
      font-size: 30px;
      color: rgba(0, 0, 0, 0.85);
      justify-content: center;
      align-items: center;
    }

    .app-loading .app-loading-logo {
      display: block;
      width: 90px;
      margin: 0 auto;
      margin-bottom: 20px;
    }

    .dot {
      position: relative;
      display: inline-block;
      width: 48px;
      height: 48px;
      margin-top: 30px;
      font-size: 32px;
      transform: rotate(45deg);
      box-sizing: border-box;
      animation: antRotate 1.2s infinite linear;
    }

    .dot i {
      position: absolute;
      display: block;
      width: 20px;
      height: 20px;
      background-color: #0065cc;
      border-radius: 100%;
      opacity: 0.3;
      transform: scale(0.75);
      animation: antSpinMove 1s infinite linear alternate;
      transform-origin: 50% 50%;
    }

    .dot i:nth-child(1) {
      top: 0;
      left: 0;
    }

    .dot i:nth-child(2) {
      top: 0;
      right: 0;
      -webkit-animation-delay: 0.4s;
      animation-delay: 0.4s;
    }

    .dot i:nth-child(3) {
      right: 0;
      bottom: 0;
      -webkit-animation-delay: 0.8s;
      animation-delay: 0.8s;
    }

    .dot i:nth-child(4) {
      bottom: 0;
      left: 0;
      -webkit-animation-delay: 1.2s;
      animation-delay: 1.2s;
    }

    @keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @-webkit-keyframes antRotate {
      to {
        -webkit-transform: rotate(405deg);
        transform: rotate(405deg);
      }
    }

    @keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }

    @-webkit-keyframes antSpinMove {
      to {
        opacity: 1;
      }
    }
  </style>
  <div class="app-loading">
    <div class="app-loading-wrap">
      <img src="/resource/img/logo.gif" class="app-loading-logo" alt="Logo"/>
      <div class="app-loading-dots">
        <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
      </div>
      <div class="app-loading-title"><%= title %></div>
    </div>
  </div>
</div>

<script type="module" src="/src/main.ts"></script>
<!--<script src="/resource/activeReportJs/ar-js-core.js"></script>
<script src="/resource/activeReportJs/ar-js-viewer.js"></script>
<script src="/resource/activeReportJs/ar-js-pdf.js"></script>
<script src="/resource/activeReportJs/ar-js-designer.js"></script>
<script src="/resource/activeReportJs/ar-js-html.js"></script>
<script src="/resource/activeReportJs/ar-js-xlsx.js"></script>
<script src="/resource/activeReportJs/locales/designer/zh-locale.js"></script>
<script src="/resource/activeReportJs/locales/ar-js-locales.js"></script>
<script src="/resource/activeReportJs/ar-js-tabular-data.js"></script>-->
<script src="/resource/idcard/idcardsvr2.min.js"></script>
<script src="/resource/lodop/LodopFuncs.js"></script>

<script>
  //var ARJS=MESCIUS.ActiveReportsJS.Core;
  /*ARJS.FontStore.registerFonts("/fonts/fontsConfig.json").then(res => {
    console.log("报表注册字体成功！")
  }).catch(e=>{
    console.error("报表注册字体失败",e)
  })*/


</script>

</body>
</html>
