package org.jeecg.modules.station.dto;

import lombok.Data;
import java.util.List;

/**
 * 体检项目信息DTO
 * 用于串口设备中间件系统的数据传输
 */
@Data
public class ExamItemInfo {
    
    /** 体检项目ID */
    private String itemId;

    /** 项目名称 */
    private String itemName;

    /** HIS代码 */
    private String hisCode;

    /** HIS名称 */
    private String hisName;

    /** 项目值 */
    private String value;

    /** 单位 */
    private String unit;

    /** 异常标志 */
    private String abnormalFlag;

    /** 参考值范围 */
    private String valueRefRange;

    /** 所属科室ID */
    private String departmentId;
    
    /** 所属科室名称 */
    private String departmentName;

    /** 依赖项目列表 */
    private List<ExamItemInfo> dependentItems;

    /**
     * 构造方法
     */
    public ExamItemInfo() {
    }

    /**
     * 构造方法
     * @param itemId 项目ID
     * @param itemName 项目名称
     */
    public ExamItemInfo(String itemId, String itemName) {
        this.itemId = itemId;
        this.itemName = itemName;
    }

    /**
     * 构造方法 - 完整参数
     * @param itemId 项目ID
     * @param itemName 项目名称
     * @param hisCode HIS代码
     * @param hisName HIS名称
     * @param departmentId 科室ID
     * @param departmentName 科室名称
     */
    public ExamItemInfo(String itemId, String itemName, String hisCode, String hisName, 
                       String departmentId, String departmentName) {
        this.itemId = itemId;
        this.itemName = itemName;
        this.hisCode = hisCode;
        this.hisName = hisName;
        this.departmentId = departmentId;
        this.departmentName = departmentName;
    }

    /**
     * 添加依赖项目
     * @param dependentItem 依赖项目
     */
    public void addDependentItem(ExamItemInfo dependentItem) {
        if (this.dependentItems == null) {
            this.dependentItems = new java.util.ArrayList<>();
        }
        this.dependentItems.add(dependentItem);
    }

    /**
     * 检查是否有依赖项目
     * @return true-有依赖项目，false-无依赖项目
     */
    public boolean hasDependentItems() {
        return this.dependentItems != null && !this.dependentItems.isEmpty();
    }

    /**
     * 获取依赖项目数量
     * @return 依赖项目数量
     */
    public int getDependentItemCount() {
        return this.dependentItems != null ? this.dependentItems.size() : 0;
    }

    @Override
    public String toString() {
        return "ExamItemInfo{" +
                "itemId='" + itemId + '\'' +
                ", itemName='" + itemName + '\'' +
                ", hisCode='" + hisCode + '\'' +
                ", hisName='" + hisName + '\'' +
                ", value='" + value + '\'' +
                ", unit='" + unit + '\'' +
                ", abnormalFlag='" + abnormalFlag + '\'' +
                ", valueRefRange='" + valueRefRange + '\'' +
                ", departmentId='" + departmentId + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", dependentItemCount=" + getDependentItemCount() +
                '}';
    }
}
